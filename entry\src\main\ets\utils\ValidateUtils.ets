/**
 * 验证工具类
 */
export class ValidateUtils {
  
  /**
   * 验证手机号是否为纯数字
   * @param phone 手机号
   * @returns boolean
   */
  static isValidPhone(phone: string): boolean {
    if (!phone || phone.trim().length === 0) {
      return false;
    }
    // 只验证是否为纯数字
    const result = /^\d+$/.test(phone.trim());
    return result;
  }

  /**
   * 验证密码（现在不要求格式）
   * @param password 密码
   * @returns boolean
   */
  static isValidPassword(password: string): boolean {
    // 只要不为空即可
    const result = password && password.trim().length > 0;
    return Boolean(result);
  }

  /**
   * 验证两次密码是否一致
   * @param password 密码
   * @param confirmPassword 确认密码
   * @returns boolean
   */
  static isPasswordMatch(password: string, confirmPassword: string): boolean {
    return password === confirmPassword;
  }
}



