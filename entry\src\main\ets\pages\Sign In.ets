import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { HttpUtils, ApiConfig, LoginRequest } from '../utils/HttpUtils';
import { ValidateUtils } from '../utils/ValidateUtils';

@Entry
@Component
struct Sign2 {
  @State phone: string = '';
  @State password: string = '';
  @State activeInput: number = 100;
  @State isLoading: boolean = false;

  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  /**
   * 账号密码登录方法
   */
  async loginWithPassword() {
    if (!ValidateUtils.isValidPhone(this.phone)) {
      promptAction.showToast({
        message: '请输入正确的手机号'
      });
      return;
    }

    if (!ValidateUtils.isValidPassword(this.password)) {
      promptAction.showToast({
        message: '请输入密码'
      });
      return;
    }

    try {
      this.isLoading = true;
      
      let requestData: LoginRequest = {
        phone: this.phone,
        password: this.password
      };
      const response = await HttpUtils.post(ApiConfig.LOGIN, requestData);

      if (response.code === 0) {
        promptAction.showToast({
          message: '登录成功'
        });
        
        AppStorage.setOrCreate('userInfo', response.data);
        AppStorage.setOrCreate('isLogin', true);
        
        router.replaceUrl({ url: 'pages/Home' });
      } else {
        promptAction.showToast({
          message: response.message || '登录失败，请检查账号密码'
        });
      }
    } catch (error) {
      promptAction.showToast({
        message: '网络错误，请稍后重试'
      });
    } finally {
      this.isLoading = false;
    }
  }

  @Builder
  inputRow(icon: Resource, placeholder: string, value: string, onChange: (v: string) => void, index: number, type: InputType = InputType.Normal) {
    Row() {
      Image(icon)
        .width(22)
        .height(22)
        .margin({ left: 12, right: 6 })
        .fillColor(this.activeInput == index ? '#407BFA' : '#CCCCCC')
      TextInput({ placeholder: placeholder, text: value })
        .type(type)
        .fontSize(16)
        .placeholderColor('#CCCCCC')
        .fontColor('#222222')
        .backgroundColor(Color.Transparent)
        .width('80%')
        .onChange(v => onChange(v))
        .onFocus(() => { this.activeInput = index })
    }
    .height(44)
    .backgroundColor('#fff')
    .borderRadius(22)
    .alignItems(VerticalAlign.Center)
    .border({ width: 1, color: '#F0F0F0' })
    .margin({ left: 24, right: 24, bottom: 18 })
  }

  build() {
    Column() {
      // 顶部插画
      Image($r('app.media.login_illustration'))
        .width(160)
        .height(120)
        .margin({ top: this.topRectHeight + 24, bottom: 16 })
        .alignSelf(ItemAlign.Center)
      
      // 主标题
      Text('账号密码登录')
        .fontSize(22)
        .fontWeight(FontWeight.Bold)
        .fontColor('#222222')
        .alignSelf(ItemAlign.Center)
        .margin({ bottom: 4 })
      
      // 副标题
      Text('欢迎使用智多星AI智能小助手')
        .fontSize(15)
        .fontColor('#999999')
        .alignSelf(ItemAlign.Center)
        .margin({ bottom: 24 })

      // 手机号输入框
      this.inputRow($r('app.media.yonghu'), '请输入手机号', this.phone, (v: string) => { this.phone = v }, 0, InputType.Number)
      
      // 密码输入框
      this.inputRow($r('app.media.mima'), '请输入密码', this.password, (v: string) => { this.password = v }, 1, InputType.Password)

      // 登录按钮
      Button(this.isLoading ? '登录中...' : '登录')
        .width('90%')
        .height(48)
        .backgroundColor(this.isLoading ? '#CCCCCC' : '#407BFA')
        .fontColor('#fff')
        .fontSize(18)
        .borderRadius(24)
        .alignSelf(ItemAlign.Center)
        .margin({ top: 16, bottom: 18 })
        .enabled(!this.isLoading)
        .onClick(() => {
          this.loginWithPassword();
        })

      // 注册新用户
      Text('注册新用户')
        .fontSize(15)
        .fontColor('#407BFA')
        .alignSelf(ItemAlign.Center)
        .margin({ top: 0, bottom: 24 })
        .onClick(() => {
          router.replace({ url: 'pages/Sign Up' })
        })

      Blank().flexGrow(1)

      // 底部隐私政策
      Text('登录即代表您已同意智多星AI智能小助手隐私政策')
        .fontSize(12)
        .fontColor('#D0D0D0')
        .alignSelf(ItemAlign.Center)
        .margin({ bottom: 12 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F7F8FA')
    .padding({ top: 0, bottom: this.bottomRectHeight })
  }
}
