import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { HttpUtils, ApiConfig, RegisterRequest } from '../utils/HttpUtils';
import { ValidateUtils } from '../utils/ValidateUtils';

@Entry
@Component
struct Login {
  @State phone: string = '';
  @State password: string = '';
  @State confirmPassword: string = '';
  @State activeInput: number = 100;
  @State isLoading: boolean = false;

  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  /**
   * 用户注册方法
   */
  async registerUser() {
    if (!ValidateUtils.isValidPhone(this.phone)) {
      promptAction.showToast({
        message: '请输入正确的手机号'
      });
      return;
    }

    if (!ValidateUtils.isValidPassword(this.password)) {
      promptAction.showToast({
        message: '请输入密码'
      });
      return;
    }

    if (!ValidateUtils.isPasswordMatch(this.password, this.confirmPassword)) {
      promptAction.showToast({
        message: '两次输入的密码不一致'
      });
      return;
    }

    try {
      this.isLoading = true;
      
      let requestData: RegisterRequest = {
        phone: this.phone,
        password: this.password
      };
      const response = await HttpUtils.post(ApiConfig.REGISTER, requestData);

      if (response.code === 0) {
        promptAction.showDialog({
          title: '注册成功',
          message: '恭喜您注册成功，现在可以去登录了！',
          buttons: [{
            text: '去登录',
            color: '#407BFA'
          }]
        }).then(() => {
          router.replace({ url: 'pages/Sign In' });
        });
      } else {
        promptAction.showToast({
          message: response.message || '注册失败，请重试'
        });
      }
    } catch (error) {
      promptAction.showToast({
        message: '网络错误，请稍后重试'
      });
    } finally {
      this.isLoading = false;
    }
  }

  @Builder
  inputRow(icon: Resource, placeholder: string, value: string, onChange: (v: string) => void, index: number, type: InputType = InputType.Normal) {
    Row() {
      Image(icon)
        .width(22)
        .height(22)
        .margin({ left: 12, right: 6 })
        .fillColor(this.activeInput == index ? '#407BFA' : '#CCCCCC')
      
      TextInput({ placeholder: placeholder, text: value })
        .type(type)
        .fontSize(16)
        .placeholderColor('#CCCCCC')
        .fontColor('#222222')
        .backgroundColor(Color.Transparent)
        .width('80%')
        .onChange(v => onChange(v))
        .onFocus(() => { this.activeInput = index })
    }
    .height(44)
    .backgroundColor('#fff')
    .borderRadius(22)
    .alignItems(VerticalAlign.Center)
    .border({ width: 1, color: '#F0F0F0' })
    .margin({ left: 24, right: 24, bottom: 18 })
  }

  build() {
    Column() {
      // 顶部标题
      Text('注册用户账号')
        .fontSize(28)
        .fontWeight(FontWeight.Bold)
        .fontColor('#222222')
        .margin({ top: 48, bottom: 8 })
        .alignSelf(ItemAlign.Center)

      Text('欢迎使用智多星AI智能小助手')
        .fontSize(15)
        .fontColor('#999999')
        .margin({ bottom: 32 })
        .alignSelf(ItemAlign.Center)

      // 手机号输入框
      this.inputRow($r('app.media.yonghu'), '请输入手机号', this.phone, (v: string) => { this.phone = v }, 0, InputType.Number)
      
      // 密码输入框
      this.inputRow($r('app.media.mima'), '请输入密码', this.password, (v: string) => { this.password = v }, 1, InputType.Password)
      
      // 确认密码输入框
      this.inputRow($r('app.media.querenmima'), '请确认输入的密码', this.confirmPassword, (v: string) => { this.confirmPassword = v }, 2, InputType.Password)

      // 注册按钮
      Button(this.isLoading ? '注册中...' : '立即注册')
        .width('90%')
        .height(48)
        .backgroundColor(this.isLoading ? '#CCCCCC' : '#407BFA')
        .fontColor('#fff')
        .fontSize(18)
        .borderRadius(24)
        .alignSelf(ItemAlign.Center)
        .margin({ top: 24, bottom: 18 })
        .enabled(!this.isLoading)
        .onClick(() => {
          this.registerUser();
        })

      // 已有账号，去登录
      Text('已有账号，去登录')
        .fontSize(15)
        .fontColor('#5B7BFA')
        .alignSelf(ItemAlign.Center)
        .margin({ top: 0, bottom: 32 })
        .onClick(() => {
          router.replace({ url: 'pages/Sign In' })
        })

      Blank().flexGrow(1)

      // 底部隐私政策
      Row() {
        Text('注册即代表您已同意')
          .fontSize(12)
          .fontColor('#D0D0D0')
        Text('智多星AI智能小助手隐私政策')
          .fontSize(12)
          .fontColor('#407BFA')
          .margin({ left: 2 })
      }
      .alignSelf(ItemAlign.Center)
      .margin({ bottom: 16, top: 0 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#F7F8FA')
    .padding({ top: this.topRectHeight, bottom: this.bottomRectHeight })
  }
}
