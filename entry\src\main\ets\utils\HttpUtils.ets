import { http } from '@kit.NetworkKit';

/**
 * HTTP响应接口定义
 */
export interface HttpResponse {
  code: number;
  message?: string;
  data?: Object;
}

/**
 * 登录请求数据接口
 */
export interface LoginRequest {
  phone: string;
  password: string;
}

/**
 * 注册请求数据接口
 */
export interface RegisterRequest {
  phone: string;
  password: string;
}

/**
 * 豆包AI请求数据接口
 */
export interface DoubaoRequest {
  model: string;
  messages: DoubaoMessage[];
  temperature?: number;
  max_tokens?: number;
  stream?: boolean;
}

export interface DoubaoMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * 豆包AI响应接口
 */
export interface DoubaoResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: DoubaoChoice[];
  usage: DoubaoUsage;
}

export interface DoubaoChoice {
  index: number;
  message: DoubaoMessage;
  finish_reason: string;
}

export interface DoubaoUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

/**
 * HTTP请求工具类
 */
export class HttpUtils {
  
  /**
   * 发送POST请求的通用方法
   */
  static async post(url: string, data: LoginRequest | RegisterRequest): Promise<HttpResponse> {
    try {
      let httpRequest = http.createHttp();
      
      let response = await httpRequest.request(url, {
        method: http.RequestMethod.POST,
        extraData: data,
        header: {
          "Content-Type": "application/json"
        }
      });
      
      return JSON.parse(response.result as string) as HttpResponse;
    } catch (error) {
      throw new Error('网络请求失败');
    }
  }

  /**
   * 调用豆包AI接口
   */
  static async callDoubaoAI(messages: DoubaoMessage[]): Promise<string> {
    try {
      let httpRequest = http.createHttp();
      
      const requestData: DoubaoRequest = {
        model: "ep-20250724211855-tbv75",
        messages: messages,
        temperature: 0.7,
        max_tokens: 2000, // 增加最大token数以支持更长回复
        stream: false
      };
      
      let response = await httpRequest.request(ApiConfig.DOUBAO_API, {
        method: http.RequestMethod.POST,
        extraData: requestData,
        header: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${ApiConfig.DOUBAO_API_KEY}`
        }
      });
      
      const result = JSON.parse(response.result as string) as DoubaoResponse;
      
      if (result.choices && result.choices.length > 0) {
        return result.choices[0].message.content;
      } else {
        throw new Error('AI响应格式错误');
      }
    } catch (error) {
      console.error('豆包AI调用失败:', error);
      throw new Error('AI服务暂时不可用，请稍后重试');
    }
  }
}

/**
 * API接口地址配置
 */
export class ApiConfig {
  static readonly BASE_URL = "http://www.softeem.xin:9898";
  static readonly REGISTER = `${ApiConfig.BASE_URL}/userInfo/register`;
  static readonly LOGIN = `${ApiConfig.BASE_URL}/userInfo/login`;
  
  // 豆包AI配置
  static readonly DOUBAO_API = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
  static readonly DOUBAO_API_KEY = "d246ac19-6c96-44b6-b8d7-783c83189d58";
}


