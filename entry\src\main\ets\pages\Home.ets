import { router } from '@kit.ArkUI';
import { promptAction } from '@kit.ArkUI';
import { HttpUtils, DoubaoMessage } from '../utils/HttpUtils';

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: number;
}

interface QuickQuestion {
  id: string;
  text: string;
}

@Entry
@Component
struct Home {
  @State messages: ChatMessage[] = [];
  @State inputText: string = '';
  @State isLoading: boolean = false;
  @State showQuickQuestions: boolean = true;
  @State currentQuestionPage: number = 0;
  @State eyeHeight: number = 89;
  @State isBlinking: boolean = false;
  private scroller: Scroller = new Scroller();

  @StorageProp('bottomRectHeight')
  bottomRectHeight: number = 0;
  @StorageProp('topRectHeight')
  topRectHeight: number = 0;

  private quickQuestions: QuickQuestion[][] = [
    [
      { id: '1', text: '简易程序的执法流程有哪些？' },
      { id: '2', text: '调查取证过程中需要收集哪些材料？' },
      { id: '3', text: '处罚告知之后可以直接走处罚程序吗？' },
      { id: '4', text: '从立案到结案整个过程有何时间限制？' }
    ],
    [
      { id: '5', text: '对于当事人不服从处罚的应该如何解决？' },
      { id: '6', text: '普通程序的执法流程是怎样的？' },
      { id: '7', text: '简易程序的罚款标准是多少？' },
      { id: '8', text: '在处罚执行时，哪种情况可以收取现金？' }
    ]
  ];

  // 添加消息历史记录用于AI上下文
  private conversationHistory: DoubaoMessage[] = [
    {
      role: 'system',
      content: '你是一个专业的法律助手，专门回答关于执法流程、法律程序等相关问题。请用简洁、专业的语言回答用户的问题。'
    }
  ];

  aboutToAppear() {
    this.startBlinkingAnimation();
    // 添加欢迎消息作为第一条消息
    this.addWelcomeMessage();
  }

  /**
   * 添加欢迎消息
   */
  addWelcomeMessage() {
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      content: '您好，很高兴为您服务～😊',
      isUser: false,
      timestamp: Date.now()
    };
    this.messages.push(welcomeMessage);
  }

  /**
   * 启动眨眼动画
   */
  startBlinkingAnimation() {
    setInterval(() => {
      this.blink();
    }, 3500);
  }

  /**
   * 眨眼动画
   */
  blink() {
    if (this.isBlinking) return;
    
    this.isBlinking = true;
    
    animateTo({ duration: 150 }, () => {
      this.eyeHeight = 40;
    });
    
    setTimeout(() => {
      animateTo({ duration: 150 }, () => {
        this.eyeHeight = 89;
      });
    }, 150);
    
    setTimeout(() => {
      animateTo({ duration: 150 }, () => {
        this.eyeHeight = 40;
      });
    }, 500);
    
    setTimeout(() => {
      animateTo({ duration: 150 }, () => {
        this.eyeHeight = 89;
      });
      this.isBlinking = false;
    }, 650);
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    setTimeout(() => {
      this.scroller.scrollEdge(Edge.Bottom);
    }, 100);
  }


  /**
   * 键盘弹窗
   */
  onPageShow(): void {
    this.getUIContext().setKeyboardAvoidMode(1)
  }

  /**
   * 发送消息
   */
  async sendMessage(content?: string) {
    const messageContent = content || this.inputText.trim();
    if (!messageContent) {
      return;
    }

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: messageContent,
      isUser: true,
      timestamp: Date.now()
    };
    
    this.messages.push(userMessage);
    this.inputText = '';
    this.isLoading = true;
    this.showQuickQuestions = false;
    
    // 滚动到底部
    this.scrollToBottom();

    try {
      // 添加用户消息到对话历史
      this.conversationHistory.push({
        role: 'user',
        content: messageContent
      });

      // 调用豆包AI
      const aiResponse = await HttpUtils.callDoubaoAI(this.conversationHistory);
      
      // 添加AI回复到对话历史
      this.conversationHistory.push({
        role: 'assistant',
        content: aiResponse
      });

      // 添加AI消息到界面
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        isUser: false,
        timestamp: Date.now()
      };
      
      this.messages.push(aiMessage);
      
      // 滚动到底部
      this.scrollToBottom();
    } catch (error) {
      // 如果AI调用失败，显示错误消息
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: `抱歉，AI服务暂时不可用：${error.message}。请稍后重试。`,
        isUser: false,
        timestamp: Date.now()
      };
      this.messages.push(errorMessage);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 退出登录
   */
  logout() {
    AppStorage.setOrCreate('isLogin', false);
    AppStorage.setOrCreate('userInfo', null);
    router.replaceUrl({ url: 'pages/Sign In' });
  }

  @Builder
  messageItem(message: ChatMessage) {
    Row() {
      if (message.isUser) {
        Blank().flexGrow(1)
        
        Text(message.content)
          .fontSize(16)
          .fontColor('#FFFFFF')
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .backgroundColor('#007AFF')
          .borderRadius(20)
          .maxLines(100)
          .constraintSize({ maxWidth: '85%' })
          .margin({ left: 60, right: 12 })
        
        Image($r('app.media.user_avatar'))
          .width(32)
          .height(32)
          .borderRadius(16)
          .margin({ right: 12 })
      } else {
        Image($r('app.media.ai_avatar'))
          .width(30)
          .height(30)
          .borderRadius(15)
          .fillColor('#007AFF')
          .margin({ left: 12, right: 8 })
        
        Text(message.content)
          .fontSize(16)
          .fontColor('#333333')
          .padding({ left: 16, right: 16, top: 12, bottom: 12 })
          .backgroundColor('#FFFFFF')
          .borderRadius(20)
          .maxLines(100)
          .constraintSize({ maxWidth: '85%' })
          .margin({ right: 60 })
          .shadow({ radius: 2, color: '#00000010', offsetX: 0, offsetY: 1 })
        
        Blank().flexGrow(1)
      }
    }
    .width('100%')
    .margin({ bottom: 16 })
    .alignItems(VerticalAlign.Top)
  }

  @Builder
  quickQuestionsCard() {
    if (this.showQuickQuestions) {
      Column() {
        // 猜你想问标题
        Row() {
          Text('猜你想问')
            .fontSize(16)
            .fontWeight(FontWeight.Medium)
            .fontColor('#333333')
          
          Text('?')
            .fontSize(14)
            .fontColor('#FFFFFF')
            .backgroundColor('#00C853')
            .width(20)
            .height(20)
            .borderRadius(10)
            .textAlign(TextAlign.Center)
            .margin({ left: 6 })
          
          Blank()
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ left: 16, right: 16, top: 16, bottom: 8 })
        .padding({left: 16})

        // 常用标签
        Row() {
          Text('常用')
            .fontSize(15)
            .fontColor('#007AFF')
            .fontWeight(FontWeight.Medium)
          
          Blank()
        }
        .width('100%')
        .margin({ left: 16, right: 16, bottom: 16 })
        .padding({left: 16})

        // 快捷问题轮播
        Swiper() {
          ForEach(this.quickQuestions, (questionGroup: QuickQuestion[]) => {
            Column() {
              ForEach(questionGroup, (question: QuickQuestion, index: number) => {
                Row() {
                  Text(question.text)
                    .fontSize(16)
                    .fontColor('#333333')
                    .flexGrow(1)
                    .textAlign(TextAlign.Start)
                  
                  Text('>')
                    .fontSize(16)
                    .fontColor('#CCCCCC')
                }
                .width('100%')
                .height(50)
                .padding({ left: 16, right: 16 ,bottom:22})
                .alignItems(VerticalAlign.Center)
                .onClick(() => {
                  this.sendMessage(question.text);
                })
                
                if (index < questionGroup.length - 1) {
                  Divider()
                    .color('#F5F5F5')
                    .strokeWidth(1)
                    .margin({ left: 16, right: 16 })
                }
              })
            }
          })
        }
        .height(200)
        .autoPlay(true)
        .interval(4000)
        .indicator(true)
        .indicatorStyle({
          color: '#E8E8E8',
          selectedColor: '#007AFF',
          size: 6
        })
      }
      .backgroundColor('#FFFFFF')
      .borderRadius(12)
      .margin({ left: 16, right: 16, bottom: 16})
      .shadow({ radius: 8, color: '#00000008', offsetX: 0, offsetY: 4 })
    }
  }

  build() {
    Column() {
      // 顶部导航栏 - 固定位置
      Row() {
        Image($r('app.media.back_arrow'))
          .width(24)
          .height(24)
          .fillColor('#333333')
          .onClick(() => {
            router.back();
          })
        
        Blank().layoutWeight(1)
        
        Text('智多星AI助手')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .fontColor('#333333')
          .textAlign(TextAlign.Center)

        
        Blank().layoutWeight(1)
        
        Text('退出')
          .fontSize(16)
          .fontColor('#007AFF')
          .padding({ left: 12, right: 12, top: 6, bottom: 6 })
          .border({ width: 1, color: '#007AFF' })
          .borderRadius(16)
          .onClick(() => {
            this.logout();
          })
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .backgroundColor('#FFFFFF')
      .position({ x: 0, y: 12 })
      .zIndex(999)

      // 上半部分：固定的眼睛区域
      Column() {
        Row() {
          Blank().flexGrow(1)
          Row() {
            Column()
              .width(40)
              .height(this.eyeHeight+15)
              .backgroundColor('#333333')
              .borderRadius(30)
              .margin({ right: 50 })
              .animation({ duration: 150, curve: Curve.EaseInOut })

            Column()
              .width(40)
              .height(this.eyeHeight+15)
              .backgroundColor('#333333')
              .borderRadius(30)
              .animation({ duration: 150, curve: Curve.EaseInOut })
          }
          .justifyContent(FlexAlign.Center)
          Blank().flexGrow(1)
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
        .alignItems(VerticalAlign.Center)
      }
      .width('100%')
      .height(190)
      .linearGradient({
        direction: GradientDirection.Bottom,
        colors: [['#C9CFD4', 0.0], ['#F5F7F9', 1.0]]
      })
      .justifyContent(FlexAlign.Center)
      .margin({ top: 56 })

      // 下半部分：可滚动的消息列表
      Scroll(this.scroller) {
        Column() {
          ForEach(this.messages, (message: ChatMessage, index: number) => {
            this.messageItem(message)
            
            // 在第一条消息后显示轮播图
            if (index === 0) {
              this.quickQuestionsCard()
            }
          })
          
          // 加载中提示
          if (this.isLoading) {
            Row() {
              Image($r('app.media.ai_avatar'))
                .width(30)
                .height(30)
                .borderRadius(15)
                .fillColor('#007AFF')
                .margin({ left: 12, right: 8 })
              
              Row() {
                Text('正在思考中...')
                  .fontSize(16)
                  .fontColor('#999999')
                  .padding({ left: 16, right: 8, top: 12, bottom: 12 })
                
                LoadingProgress()
                  .width(16)
                  .height(16)
                  .color('#007AFF')
                  .margin({ right: 12 })
              }
              .backgroundColor('#FFFFFF')
              .borderRadius(20)
              .alignItems(VerticalAlign.Center)
              .shadow({ radius: 4, color: '#00000015', offsetX: 0, offsetY: 2 })
              
              Blank().flexGrow(1)
            }
            .width('100%')
            .margin({ bottom: 16 })
            .alignItems(VerticalAlign.Top)
          }
        }
        .padding({ top: 16, bottom: 90 })
      }
      .layoutWeight(1)
      .width('100%')
      .linearGradient({
        direction: GradientDirection.Bottom,
        colors: [['#F5F7F9', 0.0], ['#F8FAFB', 1.0]]
      })

      // 底部输入区域 - 固定位置
      Row() {
        TextInput({ 
          placeholder: '在此输入您想了解的内容', 
          text: this.inputText 
        })
          .fontSize(16)
          .placeholderColor('#AAAAAA')
          .fontColor('#333333')
          .backgroundColor('#F6F6F8')
          .borderRadius(25)
          .padding({ left: 16, right: 16 })
          .height(50)
          .layoutWeight(1)
          .onChange((value: string) => {
            this.inputText = value;
          })
          .onSubmit(() => {
            this.sendMessage();
          })
        
        if (this.inputText.trim()) {
          Button() {
            Image($r('app.media.send_icon'))
              .width(20)
              .height(20)
              .fillColor('#FFFFFF')
          }
          .width(40)
          .height(40)
          .backgroundColor('#007AFF')
          .borderRadius(20)
          .margin({ left: 12 })
          .onClick(() => {
            this.sendMessage();
          })
        } else {
          Button() {
            Image($r('app.media.microphone'))
              .width(24)
              .height(24)
              .fillColor('#CCCCCC')
          }
          .width(50)
          .height(50)
          .backgroundColor('#F6F6F8')
          .borderRadius(25)
          .margin({ left: 12 })
          .onClick(() => {
            promptAction.showToast({ message: '语音功能暂未开放' });
          })
        }
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor('#FFFFFF')
      .position({ x: 0, y: '100%' })
      .translate({ x: 0, y: -76 })
      .zIndex(999)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#FFFFFF')
    .padding({ top:12, bottom:12 })
  }
}
